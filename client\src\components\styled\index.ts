// =============================================================================
// STYLED COMPONENTS LIBRARY
// =============================================================================

// Re-export utilities and extensions
export * from '../../styles/styledUtils';
export * from '../../styles/themeExtensions';

// Re-export common styled components
export {
  StyledContainer,
  StyledCard,
  StatCard,
  StatCardContent,
  StyledTypography,
  IconTypography,
  PageHeader,
  EmptyState,
} from '../../styles/styledUtils';

// Re-export utility functions
export {
  getPriorityColor,
  createResponsiveSpacing,
  createHoverEffect,
  createFocusRing,
} from '../../styles/styledUtils';

// Re-export theme extensions
export {
  themeExtensions,
  getPriorityColor as getThemePriorityColor,
  getStatusColor,
  createResponsiveValue,
  createHoverState,
  createFocusState,
  createDisabledState,
  createLoadingState,
  createTruncatedText,
  createGlassMorphism,
  createGradientBackground,
  cardVariants,
  buttonVariants,
} from '../../styles/themeExtensions';

// Re-export dashboard-specific components
export {
  DashboardContainer,
  DashboardContent,
  TabContent,
  DashboardAppBar,
  DashboardToolbar,
  UserSection,
  UserAvatar,
  StatsGrid,
  StatIcon,
  ActivityCard,
  ActivityHeader,
  ActivityTitle,
  ActivityMeta,
  ActivityDescription,
  PriorityChip,
  ReminderList,
  ReminderItem,
  MessageDetail,
  MessageBody,
  EmptyStateContainer,
  EmptyStateIcon,
  EmptyStateTitle,
  EmptyStateDescription,
} from './DashboardComponents';

// Email Components
export {
  EmailListContainer,
  EmailList,
  EmailItem,
  EmailItemButton,
  EmailIconContainer,
  EmailIcon,
  EmailContent,
  EmailHeader,
  EmailSubject,
  EmailMeta,
  EmailFrom,
  EmailDate,
  EmailSnippet,
  EmailTagsContainer,
  EmailTag,
  EmailEmptyStateContainer,
  EmailEmptyStateIcon,
  EmailEmptyStateTitle,
  EmailEmptyStateDescription,
} from './EmailComponents';

// Login Components
export {
  LoginPageContainer,
  LoginContainer,
  LoginCard,
  LoginCardContent,
  LoginHeader,
  LoginLogo,
  LoginTitle,
  LoginSubtitle,
  LoginDescription,
  LoginForm,
  LoginTextField,
  LoginSubmitButton,
  LoginAlert,
  LoginFooter,
  LoginToggleLink,
  LoginBackButton,
  LoginLoadingOverlay,
} from './LoginComponents';

// Layout Components
export {
  PageContainer,
  Section,
  ContentContainer,
  FlexContainer,
  GridContainer,
  HeroSection,
  CardGrid,
  SidebarLayout,
  Sidebar,
  MainContent,
  CenteredContent,
  Spacer,
} from './LayoutComponents';

// UI Components
export {
  PrimaryButton,
  SecondaryButton,
  DangerButton,
  GhostButton,
  FeatureCard,
  InfoCard,
  WarningCard,
  SuccessCard,
  ErrorCard,
  FeatureCardContent,
  FeatureIcon,
  HeroTitle,
  HeroSubtitle,
  SectionTitle,
  FeatureTitle,
  FeatureDescription,
  StatsContainer,
  StatNumber,
  StatLabel,
  LogoContainer,
  ActionButtonGroup,
  PriorityBadge,
  StatusIndicator,
} from './UIComponents';
